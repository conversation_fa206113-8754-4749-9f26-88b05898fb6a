<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatLo Settings - Hand-Drawn Layout</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: "#8AB0BB",
                        secondary: "#FF8383", 
                        tertiary: "#1B3E68",
                        supplement1: "#D5D8E0",
                        supplement2: "#89AFBA"
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"]
                    }
                }
            }
        };
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .filter-tag { transition: all 0.2s; }
        .filter-tag:hover { background-color: rgba(138, 176, 187, 0.1); border-color: #8AB0BB; }
        .filter-tag.active { background-color: #8AB0BB; color: #1f2937; }
        .model-row:hover { background-color: rgba(138, 176, 187, 0.05); }
        .model-row.selected { background-color: rgba(138, 176, 187, 0.1); border-left: 3px solid #8AB0BB; }
        .model-card:hover { background-color: rgba(138, 176, 187, 0.05); transform: translateY(-1px); }
        .model-card.selected { background-color: rgba(138, 176, 187, 0.1); border-color: #8AB0BB; }
        .slider::-webkit-slider-thumb { background: #8AB0BB; }
        .view-toggle.active { background-color: #8AB0BB; color: #1f2937; }
        .list-view { display: block; }
        .grid-view { display: none; }
        .grid-view.active { display: grid; }
        .list-view.active { display: block; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Background Overlay -->
    <div class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
        
        <!-- Settings Modal - Hand-Drawn Layout -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg w-full max-w-7xl max-h-[95vh] overflow-hidden shadow-2xl">
            
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-700">
                <h2 class="text-xl font-semibold text-supplement1">Chat Settings</h2>
                <button class="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-xmark text-lg"></i>
                </button>
            </div>

            <!-- Main Content: 2 Columns -->
            <div class="flex h-[calc(95vh-120px)]">

                <!-- Left Column: Model Selection -->
                <div class="w-3/5 border-r border-gray-700 flex flex-col">
                    
                    <!-- Search Bar -->
                    <div class="p-4 border-b border-gray-700">
                        <div class="flex items-center gap-3">
                            <div class="relative flex-1">
                                <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" placeholder="Search models..."
                                       class="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>

                            <div class="flex border border-gray-600 rounded overflow-hidden">
                                <button id="list-toggle" class="view-toggle active px-2 py-1 text-xs" onclick="switchToListView()">
                                    <i class="fa-solid fa-list"></i>
                                </button>
                                <button id="grid-toggle" class="view-toggle px-2 py-1 text-xs text-gray-400 hover:text-white hover:bg-gray-700" onclick="switchToGridView()">
                                    <i class="fa-solid fa-grid-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Tags -->
                    <div class="p-4 border-b border-gray-700">
                        <div class="flex flex-wrap gap-2">
                            <span class="filter-tag active px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                OpenAI <i class="fa-solid fa-xmark cursor-pointer"></i>
                            </span>
                            <span class="filter-tag active px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                Anthropic <i class="fa-solid fa-xmark cursor-pointer"></i>
                            </span>
                            <span class="filter-tag active px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                DeepSeek <i class="fa-solid fa-xmark cursor-pointer"></i>
                            </span>
                        </div>
                        <div class="flex flex-wrap gap-2 mt-2">
                            <span class="filter-tag px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                <i class="fa-solid fa-ship"></i> Flagship
                            </span>
                            <span class="filter-tag px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                <i class="fa-solid fa-gift"></i> Free
                            </span>
                            <span class="filter-tag px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                <i class="fa-solid fa-home"></i> Local
                            </span>
                            <span class="filter-tag px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                <i class="fa-solid fa-brain"></i> Reasoning
                            </span>
                            <span class="filter-tag px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                <i class="fa-solid fa-eye"></i> Vision
                            </span>
                            <span class="filter-tag px-2 py-1 text-xs rounded-full border border-gray-600 flex items-center gap-1">
                                <i class="fa-solid fa-star"></i> Favorites
                            </span>
                        </div>
                    </div>

                    <!-- Model List -->
                    <div class="flex-1 overflow-y-auto">
                        <!-- List View -->
                        <div id="list-view" class="list-view active">
                            <!-- Selected Model -->
                            <div class="model-row selected p-3 border-b border-gray-700 cursor-pointer">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fa-solid fa-star text-secondary"></i>
                                        <div>
                                            <div class="font-medium text-sm text-supplement1">Claude 3.5 Sonnet</div>
                                            <div class="text-xs text-gray-400">Anthropic • 200K context</div>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-primary text-gray-900 text-xs rounded-full">Selected</span>
                                </div>
                            </div>

                            <!-- Other Models -->
                            <div class="model-row p-3 border-b border-gray-700 cursor-pointer">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fa-regular fa-star text-gray-400 hover:text-secondary"></i>
                                        <div>
                                            <div class="font-medium text-sm text-supplement1">GPT-4 Turbo</div>
                                            <div class="text-xs text-gray-400">OpenAI • 128K context</div>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-tertiary text-supplement1 text-xs rounded-full">Flagship</span>
                                </div>
                            </div>

                            <div class="model-row p-3 border-b border-gray-700 cursor-pointer">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fa-solid fa-star text-secondary"></i>
                                        <div>
                                            <div class="font-medium text-sm text-supplement1">Gemini 2.0 Flash</div>
                                            <div class="text-xs text-gray-400">Google • 1M context</div>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">Free</span>
                                </div>
                            </div>

                            <div class="model-row p-3 border-b border-gray-700 cursor-pointer">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fa-regular fa-star text-gray-400 hover:text-secondary"></i>
                                        <div>
                                            <div class="font-medium text-sm text-supplement1">DeepSeek R1</div>
                                            <div class="text-xs text-gray-400">DeepSeek • 128K context</div>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-purple-600 text-white text-xs rounded-full">Reasoning</span>
                                </div>
                            </div>

                            <div class="model-row p-3 border-b border-gray-700 cursor-pointer">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fa-regular fa-star text-gray-400 hover:text-secondary"></i>
                                        <div>
                                            <div class="font-medium text-sm text-supplement1">GPT-4o</div>
                                            <div class="text-xs text-gray-400">OpenAI • 128K context</div>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">Vision</span>
                                </div>
                            </div>

                            <div class="model-row p-3 border-b border-gray-700 cursor-pointer opacity-60">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <i class="fa-regular fa-star text-gray-400"></i>
                                        <div>
                                            <div class="font-medium text-sm text-supplement1">Llama 3.1 70B</div>
                                            <div class="text-xs text-red-400">Meta • Local • Not Available</div>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-gray-600 text-gray-300 text-xs rounded-full">Local</span>
                                </div>
                            </div>
                        </div>

                        <!-- Grid View -->
                        <div id="grid-view" class="grid-view grid-cols-1 gap-4 p-4">
                            <!-- Selected Model Card -->
                            <div class="model-card selected p-4 bg-gray-700/30 border-2 border-primary rounded-lg cursor-pointer transition-all">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <i class="fa-solid fa-star text-secondary"></i>
                                        <h3 class="font-semibold text-supplement1">Claude 3.5 Sonnet</h3>
                                    </div>
                                    <span class="px-2 py-1 bg-primary text-gray-900 text-xs rounded-full">Selected</span>
                                </div>
                                <div class="text-xs text-gray-400 mb-3">Anthropic • 200K context</div>
                                <p class="text-sm text-gray-300 leading-relaxed">
                                    Most intelligent model with best-in-class performance on complex tasks. Excellent for coding, analysis, math, and creative writing with nuanced understanding.
                                </p>
                            </div>

                            <!-- Other Model Cards -->
                            <div class="model-card p-4 bg-gray-700/30 border border-gray-600 rounded-lg cursor-pointer transition-all">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <i class="fa-regular fa-star text-gray-400 hover:text-secondary"></i>
                                        <h3 class="font-semibold text-supplement1">GPT-4 Turbo</h3>
                                    </div>
                                    <span class="px-2 py-1 bg-tertiary text-supplement1 text-xs rounded-full">Flagship</span>
                                </div>
                                <div class="text-xs text-gray-400 mb-3">OpenAI • 128K context</div>
                                <p class="text-sm text-gray-300 leading-relaxed">
                                    OpenAI's most advanced model with strong reasoning capabilities. Great for complex problem-solving, coding, and detailed analysis tasks.
                                </p>
                            </div>

                            <div class="model-card p-4 bg-gray-700/30 border border-gray-600 rounded-lg cursor-pointer transition-all">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <i class="fa-solid fa-star text-secondary"></i>
                                        <h3 class="font-semibold text-supplement1">Gemini 2.0 Flash</h3>
                                    </div>
                                    <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">Free</span>
                                </div>
                                <div class="text-xs text-gray-400 mb-3">Google • 1M context</div>
                                <p class="text-sm text-gray-300 leading-relaxed">
                                    Google's fastest and most efficient model with massive context window. Perfect for processing large documents and real-time applications.
                                </p>
                            </div>

                            <div class="model-card p-4 bg-gray-700/30 border border-gray-600 rounded-lg cursor-pointer transition-all">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <i class="fa-regular fa-star text-gray-400 hover:text-secondary"></i>
                                        <h3 class="font-semibold text-supplement1">DeepSeek R1</h3>
                                    </div>
                                    <span class="px-2 py-1 bg-purple-600 text-white text-xs rounded-full">Reasoning</span>
                                </div>
                                <div class="text-xs text-gray-400 mb-3">DeepSeek • 128K context</div>
                                <p class="text-sm text-gray-300 leading-relaxed">
                                    Advanced reasoning model with step-by-step thinking process. Excels at mathematical problems, logical reasoning, and complex analysis.
                                </p>
                            </div>

                            <div class="model-card p-4 bg-gray-700/30 border border-gray-600 rounded-lg cursor-pointer transition-all">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <i class="fa-regular fa-star text-gray-400 hover:text-secondary"></i>
                                        <h3 class="font-semibold text-supplement1">GPT-4o</h3>
                                    </div>
                                    <span class="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">Vision</span>
                                </div>
                                <div class="text-xs text-gray-400 mb-3">OpenAI • 128K context</div>
                                <p class="text-sm text-gray-300 leading-relaxed">
                                    Multimodal model with vision capabilities. Can analyze images, charts, and documents while maintaining strong text generation abilities.
                                </p>
                            </div>

                            <div class="model-card p-4 bg-gray-700/30 border border-gray-600 rounded-lg cursor-pointer transition-all opacity-60">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <i class="fa-regular fa-star text-gray-400"></i>
                                        <h3 class="font-semibold text-supplement1">Llama 3.1 70B</h3>
                                    </div>
                                    <span class="px-2 py-1 bg-gray-600 text-gray-300 text-xs rounded-full">Local</span>
                                </div>
                                <div class="text-xs text-red-400 mb-3">Meta • Local • Not Available</div>
                                <p class="text-sm text-gray-400 leading-relaxed">
                                    Open-source model for local deployment. Requires local installation. Good for privacy-focused applications and offline usage.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Settings -->
                <div class="w-2/5 flex flex-col">
                    
                    <!-- Quick Presets (Top Right) -->
                    <div class="p-4 border-b border-gray-700">
                        <h3 class="text-sm font-semibold text-supplement1 mb-3">Quick Presets</h3>
                        <div class="flex gap-2">
                            <button class="px-3 py-1 text-xs border border-gray-600 rounded hover:border-primary transition-colors">Precise</button>
                            <button class="px-3 py-1 text-xs border border-primary bg-primary/10 text-primary rounded">Balanced</button>
                            <button class="px-3 py-1 text-xs border border-gray-600 rounded hover:border-primary transition-colors">Creative</button>
                            <button class="px-3 py-1 text-xs border border-gray-600 rounded hover:border-primary transition-colors border-dashed">Custom</button>
                        </div>
                    </div>

                    <!-- Settings Content -->
                    <div class="flex-1 p-4 overflow-y-auto space-y-6">
                        
                        <!-- Advanced Settings (4 Cards) -->
                        <div>
                            <h3 class="text-sm font-semibold text-supplement1 mb-3">Advanced Settings</h3>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="p-3 bg-gray-700/50 rounded border border-gray-600">
                                    <div class="text-xs text-gray-400 mb-1">Context Window</div>
                                    <div class="text-sm font-medium text-supplement1">200K tokens</div>
                                </div>
                                <div class="p-3 bg-gray-700/50 rounded border border-gray-600">
                                    <div class="text-xs text-gray-400 mb-1">Response Format</div>
                                    <div class="text-sm font-medium text-supplement1">Text</div>
                                </div>
                                <div class="p-3 bg-gray-700/50 rounded border border-gray-600">
                                    <div class="text-xs text-gray-400 mb-1">Streaming</div>
                                    <div class="text-sm font-medium text-supplement1">Enabled</div>
                                </div>
                                <div class="p-3 bg-gray-700/50 rounded border border-gray-600">
                                    <div class="text-xs text-gray-400 mb-1">Safety Filter</div>
                                    <div class="text-sm font-medium text-supplement1">Standard</div>
                                </div>
                            </div>
                        </div>

                        <!-- System Prompt -->
                        <div>
                            <h3 class="text-sm font-semibold text-supplement1 mb-3">System Prompt</h3>
                            <textarea 
                                placeholder="Enter system prompt to define AI behavior..."
                                class="w-full h-24 p-3 bg-gray-700 border border-gray-600 rounded-lg text-sm resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            ></textarea>
                        </div>

                        <!-- Parameters -->
                        <div>
                            <h3 class="text-sm font-semibold text-supplement1 mb-3">Parameters</h3>
                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between text-sm mb-2">
                                        <span class="text-gray-400">Temperature</span>
                                        <span class="text-supplement1">0.7</span>
                                    </div>
                                    <input type="range" min="0" max="2" step="0.1" value="0.7" 
                                           class="slider w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer">
                                </div>
                                
                                <div>
                                    <div class="flex justify-between text-sm mb-2">
                                        <span class="text-gray-400">Top-P</span>
                                        <span class="text-supplement1">0.95</span>
                                    </div>
                                    <input type="range" min="0.1" max="1" step="0.05" value="0.95" 
                                           class="slider w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer">
                                </div>
                            </div>
                        </div>

                        <!-- Max Tokens -->
                        <div>
                            <div class="flex justify-between text-sm mb-2">
                                <span class="text-gray-400">Max Tokens</span>
                                <span class="text-supplement1">4,096</span>
                            </div>
                            <input type="range" min="256" max="100000" step="256" value="4096" 
                                   class="slider w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer">
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="p-4 border-t border-gray-700 flex justify-end gap-3">
                        <button class="px-4 py-2 text-gray-400 hover:text-supplement1 transition-colors">Cancel</button>
                        <button class="px-4 py-2 bg-primary text-gray-900 rounded-md hover:bg-primary/90 transition-colors font-medium">
                            Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchToListView() {
            // Update toggles
            document.getElementById('list-toggle').classList.add('active');
            document.getElementById('list-toggle').classList.remove('text-gray-400');
            document.getElementById('grid-toggle').classList.remove('active');
            document.getElementById('grid-toggle').classList.add('text-gray-400');

            // Update views
            document.getElementById('list-view').classList.add('active');
            document.getElementById('grid-view').classList.remove('active');
        }

        function switchToGridView() {
            // Update toggles
            document.getElementById('grid-toggle').classList.add('active');
            document.getElementById('grid-toggle').classList.remove('text-gray-400');
            document.getElementById('list-toggle').classList.remove('active');
            document.getElementById('list-toggle').classList.add('text-gray-400');

            // Update views
            document.getElementById('grid-view').classList.add('active');
            document.getElementById('list-view').classList.remove('active');
        }
    </script>
</body>
</html>
