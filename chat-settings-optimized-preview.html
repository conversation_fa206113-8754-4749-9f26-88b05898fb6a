<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatLo Settings - Optimized Overlay Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: "#8AB0BB",
                        secondary: "#FF8383", 
                        tertiary: "#1B3E68",
                        supplement1: "#D5D8E0",
                        supplement2: "#89AFBA"
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"]
                    }
                }
            }
        };
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .model-card:hover { transform: translateY(-2px); border-color: #8AB0BB; }
        .model-card.selected { border-color: #8AB0BB; background-color: rgba(138, 176, 187, 0.1); }
        .preset-card:hover { border-color: #8AB0BB; background-color: rgba(138, 176, 187, 0.1); }
        .preset-card.active { border-color: #8AB0BB; background-color: rgba(138, 176, 187, 0.15); }
        .tab-button.active { border-bottom-color: #8AB0BB; color: #8AB0BB; }
        .view-toggle.active { background-color: #8AB0BB; color: #1f2937; }
        .filter-badge.active { background-color: #8AB0BB; color: #1f2937; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Background Overlay -->
    <div class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
        
        <!-- Settings Modal - Full Width Overlay -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg w-full max-w-7xl max-h-[95vh] overflow-hidden shadow-2xl">
            
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-700">
                <div>
                    <h2 class="text-xl font-semibold text-supplement1">Chat Settings</h2>
                    <p class="text-sm text-gray-400 mt-1">Configure your AI model and conversation preferences</p>
                </div>
                <button class="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-xmark text-lg"></i>
                </button>
            </div>

            <!-- Tabs -->
            <div class="flex space-x-6 border-b border-gray-700 px-6">
                <button class="tab-button active pb-3 px-1 border-b-2 font-medium text-sm transition-colors">
                    <div class="flex items-center gap-2">
                        <i class="fa-solid fa-robot"></i>
                        Model & Presets
                    </div>
                </button>
                <button class="tab-button pb-3 px-1 border-b-2 border-transparent font-medium text-sm text-gray-400 hover:text-gray-300 transition-colors">
                    <div class="flex items-center gap-2">
                        <i class="fa-solid fa-sliders"></i>
                        Advanced
                    </div>
                </button>
                <button class="tab-button pb-3 px-1 border-b-2 border-transparent font-medium text-sm text-gray-400 hover:text-gray-300 transition-colors">
                    <div class="flex items-center gap-2">
                        <i class="fa-solid fa-message"></i>
                        System Prompt
                    </div>
                </button>
            </div>

            <!-- Content Area -->
            <div class="p-6 overflow-y-auto" style="max-height: calc(95vh - 200px);">
                
                <!-- Search and Filters Row -->
                <div class="flex items-center gap-4 mb-6">
                    <!-- Search -->
                    <div class="flex-1 relative">
                        <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input type="text" placeholder="Search models..." 
                               class="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <!-- Filter Badges -->
                    <div class="flex gap-2">
                        <button class="filter-badge active px-3 py-2 text-xs font-medium rounded-full border border-gray-600 hover:border-primary transition-colors">
                            All Types
                        </button>
                        <button class="filter-badge px-3 py-2 text-xs font-medium rounded-full border border-gray-600 hover:border-primary transition-colors">
                            <i class="fa-solid fa-ship mr-1"></i>
                            Flagship
                        </button>
                        <button class="filter-badge px-3 py-2 text-xs font-medium rounded-full border border-gray-600 hover:border-primary transition-colors">
                            <i class="fa-solid fa-brain mr-1"></i>
                            Reasoning
                        </button>
                        <button class="filter-badge px-3 py-2 text-xs font-medium rounded-full border border-gray-600 hover:border-primary transition-colors">
                            <i class="fa-solid fa-gift mr-1"></i>
                            Free
                        </button>
                        <button class="filter-badge px-3 py-2 text-xs font-medium rounded-full border border-gray-600 hover:border-primary transition-colors">
                            <i class="fa-solid fa-star mr-1"></i>
                            Favorites
                        </button>
                    </div>
                    
                    <!-- View Toggle -->
                    <div class="flex border border-gray-600 rounded-lg overflow-hidden">
                        <button class="view-toggle active px-3 py-2 text-xs font-medium transition-colors">
                            <i class="fa-solid fa-grid-2"></i>
                        </button>
                        <button class="view-toggle px-3 py-2 text-xs font-medium text-gray-400 hover:text-white hover:bg-gray-700 transition-colors">
                            <i class="fa-solid fa-list"></i>
                        </button>
                        <button class="view-toggle px-3 py-2 text-xs font-medium text-gray-400 hover:text-white hover:bg-gray-700 transition-colors">
                            <i class="fa-solid fa-bars"></i>
                        </button>
                    </div>
                </div>

                <!-- Private Mode Notice -->
                <div class="mb-6 p-4 bg-tertiary/20 border border-tertiary rounded-lg">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-shield-halved text-primary"></i>
                        <div>
                            <span class="font-medium text-supplement1">Private Mode: OFF</span>
                            <span class="text-sm text-gray-400 ml-2">All models available (online + local)</span>
                        </div>
                        <button class="ml-auto px-3 py-1 bg-primary text-gray-900 text-xs rounded-full font-medium">
                            Toggle Private Mode
                        </button>
                    </div>
                </div>

                <!-- Model Selection Grid - Full Width -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-supplement1 mb-4 flex items-center gap-2">
                        <i class="fa-solid fa-robot text-primary"></i>
                        AI Model Selection
                        <span class="text-sm text-gray-400 font-normal ml-2">(12 models available)</span>
                    </h3>
                    
                    <!-- Grid View -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        
                        <!-- Selected Model -->
                        <div class="model-card selected p-4 border-2 rounded-lg cursor-pointer transition-all">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center gap-2">
                                    <span class="font-semibold text-supplement1">Claude 3.5 Sonnet</span>
                                    <i class="fa-solid fa-star text-secondary cursor-pointer"></i>
                                </div>
                                <span class="px-2 py-1 bg-primary text-gray-900 text-xs rounded-full font-medium">Selected</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Most capable model for complex reasoning and analysis</p>
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Context:</span>
                                    <span class="text-supplement1">200K tokens</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Provider:</span>
                                    <span class="text-supplement1">Anthropic</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Cost:</span>
                                    <span class="text-green-400">$0.003/1K</span>
                                </div>
                            </div>
                        </div>

                        <!-- Other Models -->
                        <div class="model-card p-4 border border-gray-600 rounded-lg cursor-pointer transition-all hover:border-primary">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center gap-2">
                                    <span class="font-semibold text-supplement1">GPT-4 Turbo</span>
                                    <i class="fa-regular fa-star text-gray-400 hover:text-secondary cursor-pointer"></i>
                                </div>
                                <span class="px-2 py-1 bg-tertiary text-supplement1 text-xs rounded-full">Flagship</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Latest GPT-4 with improved performance and speed</p>
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Context:</span>
                                    <span class="text-supplement1">128K tokens</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Provider:</span>
                                    <span class="text-supplement1">OpenAI</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Cost:</span>
                                    <span class="text-green-400">$0.01/1K</span>
                                </div>
                            </div>
                        </div>

                        <div class="model-card p-4 border border-gray-600 rounded-lg cursor-pointer transition-all hover:border-primary">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center gap-2">
                                    <span class="font-semibold text-supplement1">Gemini 2.0 Flash</span>
                                    <i class="fa-solid fa-star text-secondary cursor-pointer"></i>
                                </div>
                                <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">Free</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Fast, multimodal model with vision capabilities</p>
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Context:</span>
                                    <span class="text-supplement1">1M tokens</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Provider:</span>
                                    <span class="text-supplement1">Google</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Cost:</span>
                                    <span class="text-green-400">Free</span>
                                </div>
                            </div>
                        </div>

                        <div class="model-card p-4 border border-gray-600 rounded-lg cursor-pointer transition-all hover:border-primary opacity-60">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center gap-2">
                                    <span class="font-semibold text-supplement1">Llama 3.1 70B</span>
                                    <i class="fa-regular fa-star text-gray-400 hover:text-secondary cursor-pointer"></i>
                                </div>
                                <span class="px-2 py-1 bg-gray-600 text-gray-300 text-xs rounded-full">Local</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Open source model, requires local installation</p>
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Context:</span>
                                    <span class="text-supplement1">128K tokens</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">Provider:</span>
                                    <span class="text-supplement1">Meta</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-red-400">Status:</span>
                                    <span class="text-red-400">Not Available</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Presets -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-supplement1 mb-4 flex items-center gap-2">
                        <i class="fa-solid fa-palette text-primary"></i>
                        Quick Configuration Presets
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="preset-card p-4 border border-gray-600 rounded-lg cursor-pointer transition-all">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-solid fa-bullseye text-primary"></i>
                                <span class="font-semibold text-supplement1">Precise</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Deterministic, factual responses</p>
                            <div class="text-xs text-gray-500">
                                Temp: 0.2 • Top-P: 0.9 • Top-K: 20
                            </div>
                        </div>

                        <div class="preset-card active p-4 border-2 rounded-lg cursor-pointer transition-all">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-solid fa-balance-scale text-primary"></i>
                                <span class="font-semibold text-supplement1">Balanced</span>
                                <i class="fa-solid fa-check text-primary ml-auto"></i>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Good balance of creativity and accuracy</p>
                            <div class="text-xs text-gray-500">
                                Temp: 0.7 • Top-P: 0.95 • Top-K: 30
                            </div>
                        </div>

                        <div class="preset-card p-4 border border-gray-600 rounded-lg cursor-pointer transition-all">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-solid fa-lightbulb text-primary"></i>
                                <span class="font-semibold text-supplement1">Creative</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">More creative and diverse outputs</p>
                            <div class="text-xs text-gray-500">
                                Temp: 0.9 • Top-P: 0.99 • Top-K: 40
                            </div>
                        </div>

                        <div class="preset-card p-4 border border-gray-600 rounded-lg cursor-pointer transition-all border-dashed">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-solid fa-plus text-primary"></i>
                                <span class="font-semibold text-supplement1">Custom</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Create your own preset configuration</p>
                            <div class="text-xs text-gray-500">
                                Click to customize
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="flex items-center justify-between p-6 border-t border-gray-700">
                <div class="text-sm text-gray-400">
                    Changes will be applied to new conversations
                </div>
                <div class="flex items-center gap-3">
                    <button class="px-4 py-2 text-gray-400 hover:text-supplement1 transition-colors">
                        Cancel
                    </button>
                    <button class="px-4 py-2 bg-primary text-gray-900 rounded-md hover:bg-primary/90 transition-colors font-medium flex items-center gap-2">
                        <i class="fa-solid fa-save"></i>
                        Save Settings
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
