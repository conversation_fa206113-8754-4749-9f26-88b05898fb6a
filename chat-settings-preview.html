<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatLo Settings - Multi-Column Layout Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: "#8AB0BB",
                        secondary: "#FF8383", 
                        tertiary: "#1B3E68",
                        supplement1: "#D5D8E0",
                        supplement2: "#89AFBA"
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"]
                    }
                }
            }
        };
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .card-hover:hover { transform: translateY(-2px); }
        .preset-card:hover { border-color: #8AB0BB; background-color: rgba(138, 176, 187, 0.1); }
        .nav-item.active { background-color: rgba(138, 176, 187, 0.2); border-left: 3px solid #8AB0BB; }
        .slider::-webkit-slider-thumb { background: #8AB0BB; }
        .slider::-moz-range-thumb { background: #8AB0BB; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <div class="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
                <i class="fa-solid fa-cog text-primary text-xl"></i>
                <h1 class="text-xl font-semibold text-supplement1">Chat Settings</h1>
            </div>
            <div class="flex items-center gap-3">
                <button class="px-4 py-2 text-gray-400 hover:text-supplement1 transition-colors">
                    Reset to Defaults
                </button>
                <button class="px-4 py-2 bg-primary text-gray-900 rounded-md hover:bg-primary/90 transition-colors font-medium">
                    <i class="fa-solid fa-save mr-2"></i>
                    Save Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Main Layout: 3 Columns -->
    <div class="flex h-[calc(100vh-80px)]">
        
        <!-- Left Sidebar: Navigation -->
        <div class="w-64 bg-gray-800 border-r border-gray-700 p-4">
            <nav class="space-y-2">
                <div class="nav-item active px-4 py-3 rounded-lg cursor-pointer transition-all">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-robot text-primary"></i>
                        <span class="font-medium">Model Setup</span>
                    </div>
                </div>
                <div class="nav-item px-4 py-3 rounded-lg cursor-pointer hover:bg-gray-700 transition-all">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-sliders text-gray-400"></i>
                        <span>Parameters</span>
                    </div>
                </div>
                <div class="nav-item px-4 py-3 rounded-lg cursor-pointer hover:bg-gray-700 transition-all">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-message text-gray-400"></i>
                        <span>System Prompt</span>
                    </div>
                </div>
                <div class="nav-item px-4 py-3 rounded-lg cursor-pointer hover:bg-gray-700 transition-all">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-palette text-gray-400"></i>
                        <span>Presets</span>
                    </div>
                </div>
                <div class="nav-item px-4 py-3 rounded-lg cursor-pointer hover:bg-gray-700 transition-all">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-gear text-gray-400"></i>
                        <span>Advanced</span>
                    </div>
                </div>
            </nav>

            <!-- Quick Stats -->
            <div class="mt-8 p-4 bg-gray-700/50 rounded-lg">
                <h3 class="text-sm font-medium text-supplement1 mb-3">Current Settings</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Model:</span>
                        <span class="text-supplement1">Claude 3.5 Sonnet</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Temperature:</span>
                        <span class="text-supplement1">0.7</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Max Tokens:</span>
                        <span class="text-supplement1">4,096</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Center: Main Content -->
        <div class="flex-1 p-6 overflow-y-auto">
            <!-- Model Setup Section -->
            <div class="space-y-6">
                <!-- Model Selection Card -->
                <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-supplement1 mb-4 flex items-center gap-2">
                        <i class="fa-solid fa-robot text-primary"></i>
                        AI Model Selection
                    </h2>
                    
                    <!-- Search and Filter -->
                    <div class="flex gap-4 mb-4">
                        <div class="flex-1 relative">
                            <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="Search models..." 
                                   class="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <select class="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-primary">
                            <option>All Providers</option>
                            <option>OpenAI</option>
                            <option>Anthropic</option>
                            <option>Google</option>
                        </select>
                    </div>

                    <!-- Model Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Selected Model -->
                        <div class="p-4 bg-primary/10 border-2 border-primary rounded-lg card-hover transition-all">
                            <div class="flex items-start justify-between mb-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-lg font-semibold text-supplement1">Claude 3.5 Sonnet</span>
                                    <i class="fa-solid fa-star text-secondary"></i>
                                </div>
                                <span class="px-2 py-1 bg-primary text-gray-900 text-xs rounded-full font-medium">Selected</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Most capable model for complex tasks</p>
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-500">Context: 200K tokens</span>
                                <span class="text-green-400">$0.003/1K tokens</span>
                            </div>
                        </div>

                        <!-- Other Models -->
                        <div class="p-4 bg-gray-700/50 border border-gray-600 rounded-lg hover:border-gray-500 card-hover transition-all cursor-pointer">
                            <div class="flex items-start justify-between mb-2">
                                <div class="flex items-center gap-2">
                                    <span class="text-lg font-semibold text-supplement1">GPT-4 Turbo</span>
                                    <i class="fa-regular fa-star text-gray-400 hover:text-secondary cursor-pointer"></i>
                                </div>
                                <span class="px-2 py-1 bg-tertiary text-supplement1 text-xs rounded-full">Flagship</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Latest GPT-4 with improved performance</p>
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-500">Context: 128K tokens</span>
                                <span class="text-green-400">$0.01/1K tokens</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Presets Card -->
                <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-supplement1 mb-4 flex items-center gap-2">
                        <i class="fa-solid fa-palette text-primary"></i>
                        Quick Configuration Presets
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="preset-card p-4 border border-gray-600 rounded-lg cursor-pointer transition-all">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-solid fa-bullseye text-primary"></i>
                                <span class="font-semibold text-supplement1">Precise</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Deterministic, factual responses</p>
                            <div class="text-xs text-gray-500">
                                Temp: 0.2 • Top-P: 0.9 • Top-K: 20
                            </div>
                        </div>

                        <div class="preset-card p-4 border border-primary bg-primary/10 rounded-lg cursor-pointer transition-all">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-solid fa-balance-scale text-primary"></i>
                                <span class="font-semibold text-supplement1">Balanced</span>
                                <i class="fa-solid fa-check text-primary ml-auto"></i>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">Good balance of creativity and accuracy</p>
                            <div class="text-xs text-gray-500">
                                Temp: 0.7 • Top-P: 0.95 • Top-K: 30
                            </div>
                        </div>

                        <div class="preset-card p-4 border border-gray-600 rounded-lg cursor-pointer transition-all">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fa-solid fa-lightbulb text-primary"></i>
                                <span class="font-semibold text-supplement1">Creative</span>
                            </div>
                            <p class="text-sm text-gray-400 mb-3">More creative and diverse outputs</p>
                            <div class="text-xs text-gray-500">
                                Temp: 0.9 • Top-P: 0.99 • Top-K: 40
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Sidebar: Live Preview & Summary -->
        <div class="w-80 bg-gray-800 border-l border-gray-700 p-4 space-y-6">
            <!-- Live Preview -->
            <div class="bg-gray-700/50 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-supplement1 mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-eye text-primary"></i>
                    Live Preview
                </h3>
                <div class="bg-gray-900 rounded-lg p-3 border border-gray-600">
                    <div class="flex items-start gap-3">
                        <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-robot text-gray-900 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="text-sm text-supplement1 mb-1">Claude 3.5 Sonnet</div>
                            <div class="text-sm text-gray-300">
                                Hello! I'm Claude, an AI assistant created by Anthropic. I'm here to help you with a wide variety of tasks. How can I assist you today?
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Summary -->
            <div class="bg-gray-700/50 rounded-lg p-4">
                <h3 class="text-sm font-semibold text-supplement1 mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-list text-primary"></i>
                    Settings Summary
                </h3>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Model:</span>
                        <span class="text-supplement1 font-medium">Claude 3.5 Sonnet</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Preset:</span>
                        <span class="text-primary font-medium">Balanced</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Temperature:</span>
                        <span class="text-supplement1">0.7</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">Max Tokens:</span>
                        <span class="text-supplement1">4,096</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-400">System Prompt:</span>
                        <span class="text-gray-500">Default</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="space-y-2">
                <button class="w-full px-4 py-2 bg-primary text-gray-900 rounded-lg hover:bg-primary/90 transition-colors font-medium">
                    <i class="fa-solid fa-play mr-2"></i>
                    Test Configuration
                </button>
                <button class="w-full px-4 py-2 bg-gray-700 text-supplement1 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="fa-solid fa-download mr-2"></i>
                    Export Settings
                </button>
                <button class="w-full px-4 py-2 bg-gray-700 text-supplement1 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="fa-solid fa-upload mr-2"></i>
                    Import Settings
                </button>
            </div>
        </div>
    </div>
</body>
</html>
